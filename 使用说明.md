# 裁判文书数据导入系统使用说明

## 系统概述

根据您的需求，我已经为您创建了一个完整的裁判文书数据导入系统，具备以下功能：

✅ **MongoDB数据库设计** - 优化的数据结构和索引
✅ **Web可视化面板** - 实时监控导入进度
✅ **断点续传功能** - 支持中断后继续导入
✅ **实时统计监控** - 导入速度和状态跟踪
✅ **批量数据处理** - 高效处理大量数据

## 快速开始

### 1. 系统已启动
系统已经成功启动，Web界面地址：**http://localhost:5000**

### 2. 数据文件状态
✅ 已检测到 **44个CSV文件** (2021-2024年数据)
✅ MongoDB连接正常
✅ 所有依赖包已安装

### 3. 使用步骤

#### 第一步：初始化数据库
1. 在Web界面点击 **"初始化数据库"** 按钮
2. 系统会自动创建数据库结构和索引

#### 第二步：开始导入
1. 勾选 **"断点续传"** 选项（推荐）
2. 点击 **"开始导入"** 按钮
3. 系统开始处理数据文件

#### 第三步：监控进度
- **实时查看**：导入进度、处理速度、文件状态
- **统计信息**：总文档数、按年份/类型/地区分布
- **文件列表**：每个文件的处理状态

## 系统特性

### 🗄️ 数据库设计
- **集合结构**：documents（主文档）、import_progress（进度跟踪）、statistics（统计信息）
- **智能索引**：案号、时间、地区、案件类型等多维度索引
- **全文搜索**：支持案件内容全文检索

### 📊 数据处理
- **字段映射**：15个字段完整映射到MongoDB文档
- **数据清洗**：自动处理空值、格式化日期
- **智能提取**：法院级别、省份信息自动识别

### 🔄 断点续传
- **进度保存**：每个文件的导入进度实时保存
- **中断恢复**：系统重启后可从中断处继续
- **状态跟踪**：pending、processing、completed、failed

### ⚡ 性能优化
- **批量处理**：默认1000条记录一批
- **分块读取**：避免内存溢出
- **并发监控**：多线程状态更新

## 数据结构示例

导入后的MongoDB文档结构：
```json
{
  "_id": "ObjectId",
  "case_number": "（2021）京0105民初12345号",
  "case_name": "张三与李四合同纠纷案",
  "court": "北京市朝阳区人民法院",
  "region": "北京市",
  "case_type": "民事案件",
  "judgment_date": "2021-03-15T00:00:00Z",
  "year": 2021,
  "month": 3,
  "court_level": "基层法院",
  "region_province": "北京市",
  "import_info": {
    "file_source": "2021年03月裁判文书数据.csv",
    "import_time": "2024-08-09T11:43:46Z",
    "file_row_number": 12345
  }
}
```

## 监控指标

### 实时状态
- **总文件数** / **已处理文件数**
- **已导入行数** / **导入速度**（行/秒）
- **当前处理文件**
- **进度条**显示

### 数据库统计
- **总文档数量**
- **按年份分布**：2021-2024年数据分布
- **按案件类型分布**：民事、刑事、行政等
- **按地区分布**：各省市数据分布

## 常见操作

### 暂停/恢复导入
- 点击 **"停止导入"** 暂停处理
- 再次点击 **"开始导入"** 继续（支持断点续传）

### 查看详细日志
- Web界面底部有 **"系统日志"** 区域
- 显示实时处理状态和错误信息

### 数据查询
导入完成后，可通过MongoDB客户端查询：
```javascript
// 查询2021年民事案件
db.documents.find({
  "year": 2021,
  "case_type": "民事案件"
})

// 按法院统计案件数量
db.documents.aggregate([
  {"$group": {"_id": "$court", "count": {"$sum": 1}}},
  {"$sort": {"count": -1}}
])
```

## 性能预估

基于测试结果：
- **处理速度**：约1000-2000行/秒
- **单文件时间**：100万行约8-15分钟
- **总预估时间**：44个文件约6-12小时

## 注意事项

1. **保持系统运行**：导入过程中请保持系统和MongoDB服务运行
2. **磁盘空间**：确保有足够磁盘空间存储数据
3. **内存使用**：大文件处理时内存使用会增加
4. **网络稳定**：如使用远程MongoDB，确保网络稳定

## 故障处理

### 导入中断
- 系统支持断点续传，重新启动即可继续
- 查看日志了解中断原因

### 内存不足
- 可在config.py中调小BATCH_SIZE参数
- 重启系统释放内存

### 数据重复
- 系统使用案号作为唯一标识，自动避免重复

## 技术支持

如遇问题，请查看：
1. **系统日志**：Web界面日志区域
2. **文件日志**：import.log文件
3. **MongoDB日志**：MongoDB服务日志

---

**系统已就绪，可以开始使用！** 🚀
