#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
系统配置参数
"""

import os
from pathlib import Path

class Config:
    """系统配置类"""
    
    # MongoDB配置
    MONGODB_CONNECTION_STRING = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
    MONGODB_DATABASE_NAME = os.getenv('MONGODB_DB', 'judgment_documents')
    
    # 数据文件配置
    DATA_BASE_PATH = Path("裁判文书")
    SUPPORTED_YEARS = ["2021年", "2022年", "2023年", "2024年"]
    
    # 导入配置
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', 1000))  # 批量插入大小
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', 3))   # 最大重试次数
    CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', 1000))  # CSV读取块大小
    
    # Web应用配置
    WEB_HOST = os.getenv('WEB_HOST', '0.0.0.0')
    WEB_PORT = int(os.getenv('WEB_PORT', 5000))
    SECRET_KEY = os.getenv('SECRET_KEY', 'judgment_import_secret_key_2024')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'import.log')
    
    # 性能配置
    STATUS_UPDATE_INTERVAL = int(os.getenv('STATUS_UPDATE_INTERVAL', 2))  # 状态更新间隔(秒)
    PROGRESS_SAVE_INTERVAL = int(os.getenv('PROGRESS_SAVE_INTERVAL', 100))  # 进度保存间隔(行数)
    
    @classmethod
    def get_data_files(cls):
        """获取所有数据文件路径"""
        files = []
        for year in cls.SUPPORTED_YEARS:
            year_path = cls.DATA_BASE_PATH / year
            if year_path.exists():
                for file in year_path.glob("*.csv"):
                    files.append(str(file))
        return sorted(files)
    
    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        
        # 检查数据目录
        if not cls.DATA_BASE_PATH.exists():
            errors.append(f"数据目录不存在: {cls.DATA_BASE_PATH}")
        
        # 检查数据文件
        files = cls.get_data_files()
        if not files:
            errors.append("未找到任何CSV数据文件")
        
        return errors

# 开发环境配置
class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

# 生产环境配置
class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = 'INFO'
    BATCH_SIZE = 2000  # 生产环境使用更大的批次

# 测试环境配置
class TestingConfig(Config):
    MONGODB_DATABASE_NAME = 'judgment_documents_test'
    BATCH_SIZE = 100
    DEBUG = True

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.getenv('FLASK_ENV', 'default')
    return config_map.get(env, DevelopmentConfig)
