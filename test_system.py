#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
测试裁判文书数据导入系统的各个功能模块
"""

import unittest
import os
import tempfile
import pandas as pd
from datetime import datetime
from database_design import JudgmentDatabaseDesign
from data_importer import DataImporter
from config import get_config
import logging

# 设置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestJudgmentSystem(unittest.TestCase):
    """系统测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config = get_config()
        cls.test_db_name = "judgment_documents_test"
        
    def setUp(self):
        """每个测试方法的初始化"""
        self.db_design = JudgmentDatabaseDesign(
            db_name=self.test_db_name
        )
        self.importer = DataImporter(
            db_name=self.test_db_name
        )
        
    def tearDown(self):
        """每个测试方法的清理"""
        # 清理测试数据库
        try:
            self.db_design.client.drop_database(self.test_db_name)
        except:
            pass
        
        if hasattr(self, 'db_design'):
            self.db_design.close_connection()
        if hasattr(self, 'importer'):
            self.importer.close_connection()
    
    def test_database_design(self):
        """测试数据库设计"""
        logger.info("测试数据库设计...")
        
        # 测试数据库初始化
        self.db_design.initialize_database()
        
        # 验证集合是否创建
        collections = self.db_design.db.list_collection_names()
        self.assertIn('documents', collections)
        self.assertIn('import_progress', collections)
        self.assertIn('statistics', collections)
        
        # 验证索引是否创建
        indexes = list(self.db_design.collection.list_indexes())
        self.assertGreater(len(indexes), 1)  # 至少有_id索引和其他索引
        
        logger.info("✓ 数据库设计测试通过")
    
    def create_test_csv(self, filename, num_rows=100):
        """创建测试CSV文件"""
        data = []
        for i in range(num_rows):
            data.append({
                '原始链接': f'https://test.com/doc{i}',
                '案号': f'(2021)测{i:04d}号',
                '案件名称': f'测试案件{i}',
                '法院': '测试人民法院',
                '所属地区': '测试市',
                '案件类型': '民事案件',
                '案件类型编码': 1,
                '来源': 'test.com',
                '审理程序': '一审',
                '裁判日期': '2021-01-01',
                '公开日期': '2021-01-02',
                '当事人': f'张三{i}；李四{i}',
                '案由': '合同纠纷',
                '法律依据': '《民法典》',
                '全文': f'这是测试案件{i}的全文内容...'
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8')
        return filename
    
    def test_data_processing(self):
        """测试数据处理功能"""
        logger.info("测试数据处理...")
        
        # 创建测试数据
        test_data = {
            '原始链接': 'https://test.com/doc1',
            '案号': '(2021)测0001号',
            '案件名称': '测试案件',
            '法院': '北京市朝阳区人民法院',
            '所属地区': '北京市',
            '案件类型': '民事案件',
            '案件类型编码': 1,
            '来源': 'test.com',
            '审理程序': '一审',
            '裁判日期': '2021-01-01',
            '公开日期': '2021-01-02',
            '当事人': '张三；李四',
            '案由': '合同纠纷',
            '法律依据': '《民法典》',
            '全文': '这是测试案件的全文内容...'
        }
        
        # 测试数据处理
        row = pd.Series(test_data)
        document = self.importer.process_row(row, 'test.csv', 1)
        
        # 验证处理结果
        self.assertEqual(document['case_number'], '(2021)测0001号')
        self.assertEqual(document['case_name'], '测试案件')
        self.assertEqual(document['court_level'], '基层法院')
        self.assertEqual(document['region_province'], '北京市')
        self.assertEqual(document['year'], 2021)
        self.assertEqual(document['month'], 1)
        self.assertIsNotNone(document['search_text'])
        
        logger.info("✓ 数据处理测试通过")
    
    def test_date_parsing(self):
        """测试日期解析"""
        logger.info("测试日期解析...")
        
        # 测试各种日期格式
        test_dates = [
            ('2021-01-01', datetime(2021, 1, 1)),
            ('2021/01/01', datetime(2021, 1, 1)),
            ('2021年01月01日', datetime(2021, 1, 1)),
            ('2021-01-01 10:30:00', datetime(2021, 1, 1, 10, 30, 0)),
            ('', None),
            (None, None)
        ]
        
        for date_str, expected in test_dates:
            result = self.importer.parse_date(date_str)
            if expected is None:
                self.assertIsNone(result)
            else:
                self.assertEqual(result, expected)
        
        logger.info("✓ 日期解析测试通过")
    
    def test_court_level_extraction(self):
        """测试法院级别提取"""
        logger.info("测试法院级别提取...")
        
        test_cases = [
            ('最高人民法院', '最高法院'),
            ('北京市高级人民法院', '高级法院'),
            ('北京市第一中级人民法院', '中级法院'),
            ('北京市朝阳区人民法院', '基层法院'),
            ('其他法院', '其他'),
            ('', '未知')
        ]
        
        for court_name, expected in test_cases:
            result = self.importer.extract_court_level(court_name)
            self.assertEqual(result, expected)
        
        logger.info("✓ 法院级别提取测试通过")
    
    def test_province_extraction(self):
        """测试省份提取"""
        logger.info("测试省份提取...")
        
        test_cases = [
            ('北京市', '北京市'),
            ('上海市', '上海市'),
            ('广东省广州市', '广东省'),
            ('新疆维吾尔自治区', '新疆维吾尔自治区'),
            ('深圳市', '深圳市'),
            ('', '未知')
        ]
        
        for region, expected in test_cases:
            result = self.importer.extract_province(region)
            self.assertEqual(result, expected)
        
        logger.info("✓ 省份提取测试通过")
    
    def test_import_functionality(self):
        """测试导入功能"""
        logger.info("测试导入功能...")
        
        # 初始化数据库
        self.db_design.initialize_database()
        
        # 创建测试CSV文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            test_file = f.name
        
        try:
            self.create_test_csv(test_file, 50)
            
            # 测试导入
            result = self.importer.import_csv_file(test_file, resume=False)
            
            # 验证导入结果
            self.assertEqual(result['status'], 'completed')
            self.assertEqual(result['imported_rows'], 50)
            self.assertEqual(result['failed_rows'], 0)
            
            # 验证数据库中的数据
            count = self.importer.collection.count_documents({})
            self.assertEqual(count, 50)
            
            # 测试断点续传
            # 模拟中断后的续传
            progress = self.importer.get_import_progress(test_file)
            self.assertEqual(progress['status'], 'completed')
            self.assertEqual(progress['imported_rows'], 50)
            
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.unlink(test_file)
        
        logger.info("✓ 导入功能测试通过")
    
    def test_progress_tracking(self):
        """测试进度跟踪"""
        logger.info("测试进度跟踪...")
        
        # 初始化数据库
        self.db_design.initialize_database()
        
        test_file = "test_progress.csv"
        
        # 测试进度初始化
        progress = self.importer.get_import_progress(test_file)
        self.assertEqual(progress['status'], 'pending')
        self.assertEqual(progress['imported_rows'], 0)
        
        # 测试进度更新
        self.importer.update_import_progress(
            test_file,
            status='processing',
            total_rows=1000,
            imported_rows=500
        )
        
        # 验证更新结果
        progress = self.importer.get_import_progress(test_file)
        self.assertEqual(progress['status'], 'processing')
        self.assertEqual(progress['total_rows'], 1000)
        self.assertEqual(progress['imported_rows'], 500)
        
        logger.info("✓ 进度跟踪测试通过")
    
    def test_config_validation(self):
        """测试配置验证"""
        logger.info("测试配置验证...")
        
        # 测试配置获取
        config = get_config()
        self.assertIsNotNone(config.MONGODB_CONNECTION_STRING)
        self.assertIsNotNone(config.MONGODB_DATABASE_NAME)
        self.assertGreater(config.BATCH_SIZE, 0)
        
        logger.info("✓ 配置验证测试通过")

def run_performance_test():
    """运行性能测试"""
    logger.info("开始性能测试...")
    
    # 创建大量测试数据
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        test_file = f.name
    
    try:
        # 创建10000行测试数据
        test = TestJudgmentSystem()
        test.setUp()
        test.create_test_csv(test_file, 10000)
        
        # 初始化数据库
        test.db_design.initialize_database()
        
        # 测试导入性能
        start_time = datetime.now()
        result = test.importer.import_csv_file(test_file, resume=False)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        speed = result['imported_rows'] / duration if duration > 0 else 0
        
        logger.info(f"性能测试结果:")
        logger.info(f"  导入行数: {result['imported_rows']}")
        logger.info(f"  耗时: {duration:.2f} 秒")
        logger.info(f"  速度: {speed:.2f} 行/秒")
        
        test.tearDown()
        
    finally:
        if os.path.exists(test_file):
            os.unlink(test_file)

def main():
    """主函数"""
    print("=" * 50)
    print("裁判文书数据导入系统测试")
    print("=" * 50)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    print("\n" + "=" * 50)
    run_performance_test()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
