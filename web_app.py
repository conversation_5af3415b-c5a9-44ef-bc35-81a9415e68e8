#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web可视化面板
提供裁判文书数据导入的控制和监控界面
"""

from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import threading
import time
import json
from datetime import datetime
from data_importer import DataImporter
from database_design import JudgmentDatabaseDesign
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'judgment_import_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
importer = None
import_thread = None
status_thread = None

def initialize_importer():
    """初始化导入器"""
    global importer
    if importer is None:
        importer = DataImporter()
    return importer

def status_monitor():
    """状态监控线程"""
    while True:
        if importer and importer.is_importing:
            status = importer.get_import_status()
            socketio.emit('status_update', status)
        time.sleep(2)  # 每2秒更新一次状态

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    try:
        importer_instance = initialize_importer()
        status = importer_instance.get_import_status()
        
        # 获取数据库统计信息
        db_design = JudgmentDatabaseDesign()
        db_info = db_design.get_collection_info()
        db_design.close_connection()
        
        return jsonify({
            'success': True,
            'import_status': status,
            'database_info': db_info
        })
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/files')
def get_files():
    """获取文件列表"""
    try:
        importer_instance = initialize_importer()
        files = importer_instance.get_all_csv_files()
        
        # 获取每个文件的进度信息
        file_info = []
        for file_path in files:
            progress = importer_instance.get_import_progress(file_path)
            file_info.append({
                'path': file_path,
                'name': file_path.split('\\')[-1],
                'progress': progress
            })
        
        return jsonify({
            'success': True,
            'files': file_info
        })
    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/start_import', methods=['POST'])
def start_import():
    """开始导入"""
    global import_thread
    
    try:
        data = request.get_json()
        resume = data.get('resume', True)
        
        importer_instance = initialize_importer()
        
        if importer_instance.is_importing:
            return jsonify({'success': False, 'error': '导入正在进行中'})
        
        # 在新线程中启动导入
        def import_worker():
            try:
                result = importer_instance.start_import(resume=resume)
                socketio.emit('import_completed', result)
            except Exception as e:
                logger.error(f"导入过程出错: {e}")
                socketio.emit('import_error', {'error': str(e)})
        
        import_thread = threading.Thread(target=import_worker)
        import_thread.daemon = True
        import_thread.start()
        
        return jsonify({'success': True, 'message': '导入已开始'})
        
    except Exception as e:
        logger.error(f"启动导入失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stop_import', methods=['POST'])
def stop_import():
    """停止导入"""
    try:
        importer_instance = initialize_importer()
        importer_instance.stop_import()
        return jsonify({'success': True, 'message': '停止请求已发送'})
    except Exception as e:
        logger.error(f"停止导入失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/init_database', methods=['POST'])
def init_database():
    """初始化数据库"""
    try:
        db_design = JudgmentDatabaseDesign()
        db_design.initialize_database()
        db_design.close_connection()
        return jsonify({'success': True, 'message': '数据库初始化完成'})
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database_stats')
def get_database_stats():
    """获取数据库统计信息"""
    try:
        importer_instance = initialize_importer()
        
        # 获取基本统计
        total_docs = importer_instance.collection.count_documents({})
        
        # 按年份统计
        year_pipeline = [
            {"$group": {"_id": "$year", "count": {"$sum": 1}}},
            {"$sort": {"_id": 1}}
        ]
        year_stats = list(importer_instance.collection.aggregate(year_pipeline))
        
        # 按案件类型统计
        type_pipeline = [
            {"$group": {"_id": "$case_type", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10}
        ]
        type_stats = list(importer_instance.collection.aggregate(type_pipeline))
        
        # 按地区统计
        region_pipeline = [
            {"$group": {"_id": "$region_province", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10}
        ]
        region_stats = list(importer_instance.collection.aggregate(region_pipeline))
        
        return jsonify({
            'success': True,
            'stats': {
                'total_documents': total_docs,
                'by_year': year_stats,
                'by_case_type': type_stats,
                'by_region': region_stats
            }
        })
    except Exception as e:
        logger.error(f"获取数据库统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    logger.info('客户端已连接')
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开处理"""
    logger.info('客户端已断开连接')

@socketio.on('request_status')
def handle_status_request():
    """处理状态请求"""
    try:
        importer_instance = initialize_importer()
        status = importer_instance.get_import_status()
        emit('status_update', status)
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        emit('error', {'message': str(e)})

def create_templates():
    """创建HTML模板"""
    import os
    
    # 创建templates目录
    os.makedirs('templates', exist_ok=True)
    
    # HTML模板内容
    html_template = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>裁判文书数据导入系统</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s ease; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .status-item { text-align: center; }
        .status-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .status-label { font-size: 14px; color: #666; }
        .file-list { max-height: 400px; overflow-y: auto; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .log-area { height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .chart-container { position: relative; height: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>裁判文书数据导入系统</h1>
            <p>管理和监控裁判文书数据的导入过程</p>
        </div>
        
        <div class="card">
            <h3>控制面板</h3>
            <button id="initDbBtn" class="btn btn-primary">初始化数据库</button>
            <button id="startImportBtn" class="btn btn-success">开始导入</button>
            <button id="stopImportBtn" class="btn btn-danger" disabled>停止导入</button>
            <label><input type="checkbox" id="resumeCheckbox" checked> 断点续传</label>
        </div>
        
        <div class="card">
            <h3>导入状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="totalFiles">0</div>
                    <div class="status-label">总文件数</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="processedFiles">0</div>
                    <div class="status-label">已处理文件</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="importedRows">0</div>
                    <div class="status-label">已导入行数</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="importSpeed">0</div>
                    <div class="status-label">导入速度(行/秒)</div>
                </div>
            </div>
            <div style="margin-top: 20px;">
                <div>当前文件: <span id="currentFile">无</span></div>
                <div style="margin-top: 10px;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>数据库统计</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="totalDocs">0</div>
                    <div class="status-label">总文档数</div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="statsChart"></canvas>
            </div>
        </div>
        
        <div class="card">
            <h3>文件列表</h3>
            <div class="file-list" id="fileList">
                <div>加载中...</div>
            </div>
        </div>
        
        <div class="card">
            <h3>系统日志</h3>
            <div class="log-area" id="logArea"></div>
        </div>
    </div>
    
    <script>
        // WebSocket连接
        const socket = io();
        
        // 页面元素
        const elements = {
            initDbBtn: document.getElementById('initDbBtn'),
            startImportBtn: document.getElementById('startImportBtn'),
            stopImportBtn: document.getElementById('stopImportBtn'),
            resumeCheckbox: document.getElementById('resumeCheckbox'),
            totalFiles: document.getElementById('totalFiles'),
            processedFiles: document.getElementById('processedFiles'),
            importedRows: document.getElementById('importedRows'),
            importSpeed: document.getElementById('importSpeed'),
            currentFile: document.getElementById('currentFile'),
            progressBar: document.getElementById('progressBar'),
            totalDocs: document.getElementById('totalDocs'),
            fileList: document.getElementById('fileList'),
            logArea: document.getElementById('logArea')
        };
        
        // 日志函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\\n`;
            elements.logArea.textContent += logEntry;
            elements.logArea.scrollTop = elements.logArea.scrollHeight;
        }
        
        // API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                return await response.json();
            } catch (error) {
                addLog(`API调用失败: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }
        
        // 更新状态显示
        function updateStatus(status) {
            if (status.stats) {
                elements.totalFiles.textContent = status.stats.total_files || 0;
                elements.processedFiles.textContent = status.stats.processed_files || 0;
                elements.importedRows.textContent = (status.stats.imported_rows || 0).toLocaleString();
                elements.importSpeed.textContent = Math.round(status.stats.current_speed || 0);
            }
            
            elements.currentFile.textContent = status.current_file || '无';
            
            // 更新进度条
            if (status.stats && status.stats.total_files > 0) {
                const progress = (status.stats.processed_files / status.stats.total_files) * 100;
                elements.progressBar.style.width = progress + '%';
            }
            
            // 更新按钮状态
            elements.startImportBtn.disabled = status.is_importing;
            elements.stopImportBtn.disabled = !status.is_importing;
        }
        
        // 加载文件列表
        async function loadFileList() {
            const result = await apiCall('/api/files');
            if (result.success) {
                elements.fileList.innerHTML = result.files.map(file => `
                    <div class="file-item">
                        <span>${file.name}</span>
                        <span>${file.progress.status} (${file.progress.imported_rows}/${file.progress.total_rows})</span>
                    </div>
                `).join('');
            }
        }
        
        // 加载数据库统计
        async function loadDatabaseStats() {
            const result = await apiCall('/api/database_stats');
            if (result.success) {
                elements.totalDocs.textContent = result.stats.total_documents.toLocaleString();
            }
        }
        
        // 事件监听器
        elements.initDbBtn.addEventListener('click', async () => {
            addLog('开始初始化数据库...');
            const result = await apiCall('/api/init_database', { method: 'POST' });
            if (result.success) {
                addLog('数据库初始化成功');
            } else {
                addLog(`数据库初始化失败: ${result.error}`, 'error');
            }
        });
        
        elements.startImportBtn.addEventListener('click', async () => {
            const resume = elements.resumeCheckbox.checked;
            addLog(`开始导入数据 (断点续传: ${resume ? '是' : '否'})...`);
            const result = await apiCall('/api/start_import', {
                method: 'POST',
                body: JSON.stringify({ resume })
            });
            if (result.success) {
                addLog('导入已开始');
            } else {
                addLog(`启动导入失败: ${result.error}`, 'error');
            }
        });
        
        elements.stopImportBtn.addEventListener('click', async () => {
            addLog('请求停止导入...');
            const result = await apiCall('/api/stop_import', { method: 'POST' });
            if (result.success) {
                addLog('停止请求已发送');
            } else {
                addLog(`停止请求失败: ${result.error}`, 'error');
            }
        });
        
        // WebSocket事件监听
        socket.on('connect', () => {
            addLog('WebSocket连接成功');
            socket.emit('request_status');
        });
        
        socket.on('status_update', (status) => {
            updateStatus(status);
        });
        
        socket.on('import_completed', (result) => {
            addLog('导入完成!');
            loadDatabaseStats();
            loadFileList();
        });
        
        socket.on('import_error', (error) => {
            addLog(`导入出错: ${error.error}`, 'error');
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('系统启动');
            loadFileList();
            loadDatabaseStats();
            
            // 定期更新状态
            setInterval(() => {
                socket.emit('request_status');
                loadDatabaseStats();
            }, 5000);
        });
    </script>
</body>
</html>'''
    
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    logger.info("HTML模板已创建")

def main():
    """主函数"""
    # 创建模板文件
    create_templates()
    
    # 启动状态监控线程
    global status_thread
    status_thread = threading.Thread(target=status_monitor)
    status_thread.daemon = True
    status_thread.start()
    
    # 启动Web应用
    logger.info("启动Web应用...")
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()
