<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>裁判文书数据导入系统</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s ease; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .status-item { text-align: center; }
        .status-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .status-label { font-size: 14px; color: #666; }
        .file-list { max-height: 400px; overflow-y: auto; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .log-area { height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .chart-container { position: relative; height: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>裁判文书数据导入系统</h1>
            <p>管理和监控裁判文书数据的导入过程</p>
        </div>
        
        <div class="card">
            <h3>控制面板</h3>
            <button id="initDbBtn" class="btn btn-primary">初始化数据库</button>
            <button id="startImportBtn" class="btn btn-success">开始导入</button>
            <button id="stopImportBtn" class="btn btn-danger" disabled>停止导入</button>
            <label><input type="checkbox" id="resumeCheckbox" checked> 断点续传</label>
        </div>
        
        <div class="card">
            <h3>导入状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="totalFiles">0</div>
                    <div class="status-label">总文件数</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="processedFiles">0</div>
                    <div class="status-label">已处理文件</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="importedRows">0</div>
                    <div class="status-label">已导入行数</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="importSpeed">0</div>
                    <div class="status-label">导入速度(行/秒)</div>
                </div>
            </div>
            <div style="margin-top: 20px;">
                <div>当前文件: <span id="currentFile">无</span></div>
                <div style="margin-top: 10px;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>数据库统计</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="totalDocs">0</div>
                    <div class="status-label">总文档数</div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="statsChart"></canvas>
            </div>
        </div>
        
        <div class="card">
            <h3>文件列表</h3>
            <div class="file-list" id="fileList">
                <div>加载中...</div>
            </div>
        </div>
        
        <div class="card">
            <h3>系统日志</h3>
            <div class="log-area" id="logArea"></div>
        </div>
    </div>
    
    <script>
        // WebSocket连接
        const socket = io();
        
        // 页面元素
        const elements = {
            initDbBtn: document.getElementById('initDbBtn'),
            startImportBtn: document.getElementById('startImportBtn'),
            stopImportBtn: document.getElementById('stopImportBtn'),
            resumeCheckbox: document.getElementById('resumeCheckbox'),
            totalFiles: document.getElementById('totalFiles'),
            processedFiles: document.getElementById('processedFiles'),
            importedRows: document.getElementById('importedRows'),
            importSpeed: document.getElementById('importSpeed'),
            currentFile: document.getElementById('currentFile'),
            progressBar: document.getElementById('progressBar'),
            totalDocs: document.getElementById('totalDocs'),
            fileList: document.getElementById('fileList'),
            logArea: document.getElementById('logArea')
        };
        
        // 日志函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            elements.logArea.textContent += logEntry;
            elements.logArea.scrollTop = elements.logArea.scrollHeight;
        }
        
        // API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                return await response.json();
            } catch (error) {
                addLog(`API调用失败: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }
        
        // 更新状态显示
        function updateStatus(status) {
            if (status.stats) {
                elements.totalFiles.textContent = status.stats.total_files || 0;
                elements.processedFiles.textContent = status.stats.processed_files || 0;
                elements.importedRows.textContent = (status.stats.imported_rows || 0).toLocaleString();
                elements.importSpeed.textContent = Math.round(status.stats.current_speed || 0);
            }
            
            elements.currentFile.textContent = status.current_file || '无';
            
            // 更新进度条
            if (status.stats && status.stats.total_files > 0) {
                const progress = (status.stats.processed_files / status.stats.total_files) * 100;
                elements.progressBar.style.width = progress + '%';
            }
            
            // 更新按钮状态
            elements.startImportBtn.disabled = status.is_importing;
            elements.stopImportBtn.disabled = !status.is_importing;
        }
        
        // 加载文件列表
        async function loadFileList() {
            const result = await apiCall('/api/files');
            if (result.success) {
                elements.fileList.innerHTML = result.files.map(file => `
                    <div class="file-item">
                        <span>${file.name}</span>
                        <span>${file.progress.status} (${file.progress.imported_rows}/${file.progress.total_rows})</span>
                    </div>
                `).join('');
            }
        }
        
        // 加载数据库统计
        async function loadDatabaseStats() {
            const result = await apiCall('/api/database_stats');
            if (result.success) {
                elements.totalDocs.textContent = result.stats.total_documents.toLocaleString();
            }
        }
        
        // 事件监听器
        elements.initDbBtn.addEventListener('click', async () => {
            addLog('开始初始化数据库...');
            const result = await apiCall('/api/init_database', { method: 'POST' });
            if (result.success) {
                addLog('数据库初始化成功');
            } else {
                addLog(`数据库初始化失败: ${result.error}`, 'error');
            }
        });
        
        elements.startImportBtn.addEventListener('click', async () => {
            const resume = elements.resumeCheckbox.checked;
            addLog(`开始导入数据 (断点续传: ${resume ? '是' : '否'})...`);
            const result = await apiCall('/api/start_import', {
                method: 'POST',
                body: JSON.stringify({ resume })
            });
            if (result.success) {
                addLog('导入已开始');
            } else {
                addLog(`启动导入失败: ${result.error}`, 'error');
            }
        });
        
        elements.stopImportBtn.addEventListener('click', async () => {
            addLog('请求停止导入...');
            const result = await apiCall('/api/stop_import', { method: 'POST' });
            if (result.success) {
                addLog('停止请求已发送');
            } else {
                addLog(`停止请求失败: ${result.error}`, 'error');
            }
        });
        
        // WebSocket事件监听
        socket.on('connect', () => {
            addLog('WebSocket连接成功');
            socket.emit('request_status');
        });
        
        socket.on('status_update', (status) => {
            updateStatus(status);
        });
        
        socket.on('import_completed', (result) => {
            addLog('导入完成!');
            loadDatabaseStats();
            loadFileList();
        });
        
        socket.on('import_error', (error) => {
            addLog(`导入出错: ${error.error}`, 'error');
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('系统启动');
            loadFileList();
            loadDatabaseStats();
            
            // 定期更新状态
            setInterval(() => {
                socket.emit('request_status');
                loadDatabaseStats();
            }, 5000);
        });
    </script>
</body>
</html>