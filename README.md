# 裁判文书数据导入系统

一个专为2021-2024年裁判文书数据设计的MongoDB导入系统，具有Web可视化界面、断点续传、实时监控等功能。

## 功能特性

- 🗄️ **MongoDB数据库设计**: 优化的数据结构和索引设计
- 📊 **Web可视化面板**: 实时监控导入进度和状态
- 🔄 **断点续传**: 支持导入中断后从断点继续
- ⚡ **批量处理**: 高效的批量数据导入
- 📈 **实时统计**: 导入速度、进度统计
- 🔍 **全文搜索**: 支持案件内容全文检索
- 🏷️ **智能分类**: 自动提取法院级别、地区省份等信息

## 系统架构

```
裁判文书数据导入系统/
├── database_design.py    # 数据库结构设计
├── data_importer.py      # 数据导入核心模块
├── web_app.py           # Web可视化界面
├── config.py            # 系统配置
├── run.py              # 启动脚本
├── test_system.py      # 系统测试
├── requirements.txt    # 依赖包列表
└── 裁判文书/            # 数据文件目录
    ├── 2021年/
    ├── 2022年/
    ├── 2023年/
    └── 2024年/
```

## 快速开始

### 1. 环境准备

**系统要求:**
- Python 3.8+
- MongoDB 4.4+
- 8GB+ 内存 (推荐)

**安装依赖:**
```bash
pip install -r requirements.txt
```

### 2. 配置MongoDB

确保MongoDB服务正在运行:
```bash
# Windows
net start MongoDB

# Linux/Mac
sudo systemctl start mongod
```

### 3. 数据准备

将裁判文书CSV文件按以下结构放置:
```
裁判文书/
├── 2021年/
│   ├── 2021年01月裁判文书数据.csv
│   ├── 2021年02月裁判文书数据.csv
│   └── ...
├── 2022年/
├── 2023年/
└── 2024年/
```

### 4. 启动系统

```bash
python run.py
```

系统启动后访问: http://localhost:5000

## 使用指南

### Web界面操作

1. **初始化数据库**: 首次使用时点击"初始化数据库"按钮
2. **开始导入**: 点击"开始导入"开始数据导入
3. **监控进度**: 实时查看导入进度和统计信息
4. **断点续传**: 勾选"断点续传"选项支持中断后继续

### 数据结构

系统会将CSV数据转换为以下MongoDB文档结构:

```json
{
  "_id": "ObjectId",
  "original_url": "原始链接",
  "case_number": "案号",
  "case_name": "案件名称",
  "court": "法院",
  "region": "所属地区",
  "case_type": "案件类型",
  "case_type_code": 1,
  "source": "来源",
  "trial_procedure": "审理程序",
  "judgment_date": "2021-01-01T00:00:00Z",
  "publish_date": "2021-01-02T00:00:00Z",
  "parties": "当事人",
  "case_cause": "案由",
  "legal_basis": "法律依据",
  "full_text": "全文",
  "import_info": {
    "file_source": "文件名",
    "import_time": "2024-01-01T00:00:00Z",
    "file_row_number": 1
  },
  "year": 2021,
  "month": 1,
  "court_level": "基层法院",
  "region_province": "北京市",
  "search_text": "组合搜索字段"
}
```

### 配置选项

可通过环境变量或修改`config.py`调整配置:

```bash
# MongoDB配置
export MONGODB_URI="mongodb://localhost:27017/"
export MONGODB_DB="judgment_documents"

# 性能配置
export BATCH_SIZE=1000
export CHUNK_SIZE=1000

# Web配置
export WEB_PORT=5000
export DEBUG=False
```

## 性能优化

### 数据库索引

系统自动创建以下索引以优化查询性能:

- 案号唯一索引
- 时间范围查询索引 (裁判日期、公开日期)
- 分类查询索引 (案件类型、法院、地区)
- 复合索引 (年份+案件类型、地区+年份等)
- 全文搜索索引

### 导入性能

- **批量插入**: 默认1000条记录一批
- **并发处理**: 支持多线程状态监控
- **内存优化**: 分块读取大文件
- **断点续传**: 避免重复导入

## 监控和统计

### 实时监控指标

- 总文件数 / 已处理文件数
- 总导入行数 / 导入速度
- 当前处理文件
- 导入进度条

### 数据库统计

- 总文档数量
- 按年份分布
- 按案件类型分布
- 按地区分布

## 故障排除

### 常见问题

**1. MongoDB连接失败**
```
✗ MongoDB连接失败: [Errno 111] Connection refused
```
解决: 确保MongoDB服务正在运行

**2. 内存不足**
```
MemoryError: Unable to allocate array
```
解决: 减小BATCH_SIZE和CHUNK_SIZE配置

**3. 文件编码错误**
```
UnicodeDecodeError: 'utf-8' codec can't decode
```
解决: 检查CSV文件编码，确保为UTF-8

**4. 导入中断**
- 系统支持断点续传，重新启动导入即可从中断处继续

### 日志查看

系统日志保存在`import.log`文件中:
```bash
tail -f import.log
```

## 测试

运行系统测试:
```bash
python test_system.py
```

测试包括:
- 数据库设计测试
- 数据处理功能测试
- 导入功能测试
- 性能测试

## 技术栈

- **后端**: Python, Flask, Flask-SocketIO
- **数据库**: MongoDB, PyMongo
- **数据处理**: Pandas, NumPy
- **前端**: HTML5, JavaScript, WebSocket
- **可视化**: Chart.js

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进系统。

## 联系方式

如有问题请通过GitHub Issues联系。
