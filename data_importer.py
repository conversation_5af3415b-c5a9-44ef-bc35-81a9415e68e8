#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入核心模块
处理CSV文件并导入到MongoDB数据库
"""

import pandas as pd
import numpy as np
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
import os
import re
from pathlib import Path
import time
from typing import Dict, List, Optional, Tuple
import threading
from queue import Queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataImporter:
    """数据导入器类"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        """
        初始化数据导入器
        
        Args:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection = self.db["documents"]
        self.progress_collection = self.db["import_progress"]
        self.stats_collection = self.db["statistics"]
        
        # 导入配置
        self.batch_size = 1000  # 批量插入大小
        self.max_retries = 3    # 最大重试次数
        
        # 状态跟踪
        self.is_importing = False
        self.current_file = None
        self.import_stats = {
            'total_files': 0,
            'processed_files': 0,
            'total_rows': 0,
            'imported_rows': 0,
            'failed_rows': 0,
            'start_time': None,
            'current_speed': 0
        }
        
        # 线程安全锁
        self.lock = threading.Lock()
    
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            datetime对象或None
        """
        if pd.isna(date_str) or not date_str:
            return None
        
        # 常见日期格式
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y年%m月%d日',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_str).strip(), fmt)
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期: {date_str}")
        return None
    
    def extract_court_level(self, court_name: str) -> str:
        """
        从法院名称提取法院级别
        
        Args:
            court_name: 法院名称
            
        Returns:
            法院级别
        """
        if not court_name:
            return "未知"
        
        if "最高人民法院" in court_name:
            return "最高法院"
        elif "高级人民法院" in court_name:
            return "高级法院"
        elif "中级人民法院" in court_name:
            return "中级法院"
        elif "基层人民法院" in court_name or "人民法院" in court_name:
            return "基层法院"
        else:
            return "其他"
    
    def extract_province(self, region: str) -> str:
        """
        从地区提取省份
        
        Args:
            region: 地区名称
            
        Returns:
            省份名称
        """
        if not region:
            return "未知"
        
        # 直辖市
        municipalities = ["北京市", "上海市", "天津市", "重庆市"]
        for city in municipalities:
            if city in region:
                return city
        
        # 省份匹配
        if "省" in region:
            match = re.search(r'(\w+省)', region)
            if match:
                return match.group(1)
        
        # 自治区
        if "自治区" in region:
            match = re.search(r'(\w+自治区)', region)
            if match:
                return match.group(1)
        
        return region.split("市")[0] + "市" if "市" in region else "未知"
    
    def process_row(self, row: pd.Series, file_source: str, row_number: int) -> Dict:
        """
        处理单行数据
        
        Args:
            row: pandas Series对象
            file_source: 源文件名
            row_number: 行号
            
        Returns:
            处理后的文档字典
        """
        # 解析日期
        judgment_date = self.parse_date(row.get('裁判日期'))
        publish_date = self.parse_date(row.get('公开日期'))
        
        # 提取年月
        year = judgment_date.year if judgment_date else None
        month = judgment_date.month if judgment_date else None
        
        # 处理文本字段
        def clean_text(text):
            if pd.isna(text):
                return None
            return str(text).strip() if str(text).strip() else None
        
        # 构建搜索文本
        search_parts = []
        for field in ['案件名称', '当事人', '案由', '全文']:
            value = clean_text(row.get(field))
            if value:
                search_parts.append(value)
        search_text = ' '.join(search_parts) if search_parts else None
        
        # 构建文档
        document = {
            'original_url': clean_text(row.get('原始链接')),
            'case_number': clean_text(row.get('案号')),
            'case_name': clean_text(row.get('案件名称')),
            'court': clean_text(row.get('法院')),
            'region': clean_text(row.get('所属地区')),
            'case_type': clean_text(row.get('案件类型')),
            'case_type_code': int(row.get('案件类型编码', 0)) if pd.notna(row.get('案件类型编码')) else None,
            'source': clean_text(row.get('来源')),
            'trial_procedure': clean_text(row.get('审理程序')),
            'judgment_date': judgment_date,
            'publish_date': publish_date,
            'parties': clean_text(row.get('当事人')),
            'case_cause': clean_text(row.get('案由')),
            'legal_basis': clean_text(row.get('法律依据')),
            'full_text': clean_text(row.get('全文')),
            
            # 元数据
            'import_info': {
                'file_source': file_source,
                'import_time': datetime.now(),
                'file_row_number': row_number
            },
            
            # 索引优化字段
            'year': year,
            'month': month,
            'court_level': self.extract_court_level(clean_text(row.get('法院'))),
            'region_province': self.extract_province(clean_text(row.get('所属地区'))),
            'search_text': search_text
        }
        
        return document
    
    def get_import_progress(self, file_path: str) -> Dict:
        """
        获取文件导入进度
        
        Args:
            file_path: 文件路径
            
        Returns:
            进度信息字典
        """
        progress = self.progress_collection.find_one({"_id": file_path})
        if not progress:
            return {
                "file_path": file_path,
                "total_rows": 0,
                "imported_rows": 0,
                "status": "pending",
                "last_row_processed": 0
            }
        return progress
    
    def update_import_progress(self, file_path: str, **kwargs):
        """
        更新导入进度
        
        Args:
            file_path: 文件路径
            **kwargs: 要更新的字段
        """
        update_data = {"$set": kwargs}
        self.progress_collection.update_one(
            {"_id": file_path},
            update_data,
            upsert=True
        )
    
    def import_csv_file(self, file_path: str, resume: bool = True) -> Dict:
        """
        导入单个CSV文件
        
        Args:
            file_path: CSV文件路径
            resume: 是否支持断点续传
            
        Returns:
            导入结果字典
        """
        logger.info(f"开始导入文件: {file_path}")
        
        # 获取进度信息
        progress = self.get_import_progress(file_path)
        start_row = progress['last_row_processed'] if resume else 0
        
        # 更新状态
        self.update_import_progress(
            file_path,
            status="processing",
            start_time=datetime.now()
        )
        
        try:
            # 读取CSV文件信息
            total_rows = sum(1 for _ in open(file_path, 'r', encoding='utf-8')) - 1  # 减去标题行
            
            self.update_import_progress(file_path, total_rows=total_rows)
            
            # 分块读取文件
            chunk_size = self.batch_size
            imported_count = 0
            failed_count = 0
            
            file_name = os.path.basename(file_path)
            
            # 使用pandas分块读取
            for chunk_num, chunk in enumerate(pd.read_csv(
                file_path, 
                encoding='utf-8', 
                chunksize=chunk_size,
                skiprows=range(1, start_row + 1) if start_row > 0 else None
            )):
                
                if not self.is_importing:
                    logger.info("导入被中断")
                    break
                
                batch_documents = []
                current_row = start_row + chunk_num * chunk_size
                
                for idx, row in chunk.iterrows():
                    try:
                        document = self.process_row(row, file_name, current_row + idx - start_row)
                        batch_documents.append(document)
                    except Exception as e:
                        logger.error(f"处理行 {current_row + idx} 失败: {e}")
                        failed_count += 1
                
                # 批量插入
                if batch_documents:
                    try:
                        # 使用upsert避免重复插入
                        for doc in batch_documents:
                            if doc.get('case_number'):
                                self.collection.update_one(
                                    {'case_number': doc['case_number']},
                                    {'$set': doc},
                                    upsert=True
                                )
                            else:
                                self.collection.insert_one(doc)
                        
                        imported_count += len(batch_documents)
                        
                        # 更新进度
                        with self.lock:
                            self.import_stats['imported_rows'] += len(batch_documents)
                            self.import_stats['failed_rows'] += failed_count
                        
                        self.update_import_progress(
                            file_path,
                            imported_rows=start_row + imported_count,
                            last_row_processed=current_row + len(chunk)
                        )
                        
                        logger.info(f"已导入 {imported_count} 行，失败 {failed_count} 行")
                        
                    except Exception as e:
                        logger.error(f"批量插入失败: {e}")
                        failed_count += len(batch_documents)
            
            # 完成导入
            self.update_import_progress(
                file_path,
                status="completed",
                end_time=datetime.now(),
                imported_rows=start_row + imported_count
            )
            
            result = {
                "file_path": file_path,
                "total_rows": total_rows,
                "imported_rows": imported_count,
                "failed_rows": failed_count,
                "status": "completed"
            }
            
            logger.info(f"文件导入完成: {file_path}, 成功: {imported_count}, 失败: {failed_count}")
            return result
            
        except Exception as e:
            logger.error(f"导入文件失败: {file_path}, 错误: {e}")
            self.update_import_progress(
                file_path,
                status="failed",
                end_time=datetime.now(),
                error_message=str(e)
            )
            return {
                "file_path": file_path,
                "status": "failed",
                "error": str(e)
            }
    
    def get_all_csv_files(self) -> List[str]:
        """获取所有CSV文件路径"""
        files = []
        base_path = Path("裁判文书")
        
        for year_dir in ["2021年", "2022年", "2023年", "2024年"]:
            year_path = base_path / year_dir
            if year_path.exists():
                for file in year_path.glob("*.csv"):
                    files.append(str(file))
        
        return sorted(files)
    
    def start_import(self, resume: bool = True) -> Dict:
        """
        开始导入所有文件
        
        Args:
            resume: 是否支持断点续传
            
        Returns:
            导入结果
        """
        if self.is_importing:
            return {"status": "error", "message": "导入正在进行中"}
        
        self.is_importing = True
        files = self.get_all_csv_files()
        
        # 初始化统计信息
        with self.lock:
            self.import_stats = {
                'total_files': len(files),
                'processed_files': 0,
                'total_rows': 0,
                'imported_rows': 0,
                'failed_rows': 0,
                'start_time': datetime.now(),
                'current_speed': 0
            }
        
        logger.info(f"开始导入 {len(files)} 个文件")
        
        results = []
        for file_path in files:
            if not self.is_importing:
                break
            
            self.current_file = file_path
            result = self.import_csv_file(file_path, resume)
            results.append(result)
            
            with self.lock:
                self.import_stats['processed_files'] += 1
        
        self.is_importing = False
        self.current_file = None
        
        return {
            "status": "completed",
            "results": results,
            "stats": self.import_stats
        }
    
    def stop_import(self):
        """停止导入"""
        self.is_importing = False
        logger.info("导入停止请求已发送")
    
    def get_import_status(self) -> Dict:
        """获取当前导入状态"""
        with self.lock:
            stats = self.import_stats.copy()
        
        # 计算导入速度
        if stats['start_time'] and self.is_importing:
            elapsed = (datetime.now() - stats['start_time']).total_seconds()
            if elapsed > 0:
                stats['current_speed'] = stats['imported_rows'] / elapsed
        
        return {
            "is_importing": self.is_importing,
            "current_file": self.current_file,
            "stats": stats
        }
    
    def close_connection(self):
        """关闭数据库连接"""
        self.client.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数 - 测试导入功能"""
    importer = DataImporter()
    
    try:
        # 获取文件列表
        files = importer.get_all_csv_files()
        print(f"找到 {len(files)} 个CSV文件")
        
        # 测试导入第一个文件
        if files:
            result = importer.import_csv_file(files[0])
            print(f"导入结果: {result}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        importer.close_connection()

if __name__ == "__main__":
    main()
