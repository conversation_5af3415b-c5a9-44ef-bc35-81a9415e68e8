#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
启动裁判文书数据导入系统
"""

import os
import sys
import logging
from pathlib import Path
from config import get_config
from web_app import app, socketio, create_templates
from database_design import JudgmentDatabaseDesign

def setup_logging(config):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖"""
    try:
        import pymongo
        import pandas
        import flask
        import flask_socketio
        print("✓ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_mongodb_connection(config):
    """检查MongoDB连接"""
    try:
        from pymongo import MongoClient
        client = MongoClient(config.MONGODB_CONNECTION_STRING, serverSelectionTimeoutMS=5000)
        client.server_info()  # 触发连接
        client.close()
        print("✓ MongoDB连接正常")
        return True
    except Exception as e:
        print(f"✗ MongoDB连接失败: {e}")
        print("请确保MongoDB服务正在运行")
        return False

def initialize_system(config):
    """初始化系统"""
    print("正在初始化系统...")
    
    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 创建HTML模板
    create_templates()
    print("✓ 模板文件已创建")
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print("✗ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✓ 配置验证通过")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("裁判文书数据导入系统")
    print("=" * 50)
    
    # 获取配置
    config = get_config()
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    print(f"运行环境: {os.getenv('FLASK_ENV', 'development')}")
    print(f"数据库: {config.MONGODB_DATABASE_NAME}")
    print(f"Web端口: {config.WEB_PORT}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查MongoDB连接
    if not check_mongodb_connection(config):
        sys.exit(1)
    
    # 初始化系统
    if not initialize_system(config):
        sys.exit(1)
    
    # 显示数据文件信息
    files = config.get_data_files()
    print(f"✓ 找到 {len(files)} 个数据文件")
    
    print("\n系统启动中...")
    print(f"Web界面地址: http://localhost:{config.WEB_PORT}")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 启动Web应用
        socketio.run(
            app, 
            host=config.WEB_HOST, 
            port=config.WEB_PORT, 
            debug=config.DEBUG,
            use_reloader=False  # 避免重复启动
        )
    except KeyboardInterrupt:
        print("\n正在关闭系统...")
        logger.info("系统正常关闭")
    except Exception as e:
        print(f"\n系统启动失败: {e}")
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
