#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据库设计模块
为裁判文书数据设计合适的数据库结构和索引
"""

from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JudgmentDatabaseDesign:
    """裁判文书数据库设计类"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        """
        初始化数据库连接
        
        Args:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection_name = "documents"
        self.collection = self.db[self.collection_name]
        
        # 导入进度跟踪集合
        self.progress_collection = self.db["import_progress"]
        
        # 统计信息集合
        self.stats_collection = self.db["statistics"]
    
    def get_document_schema(self):
        """
        定义文档结构模式
        
        Returns:
            dict: 文档结构示例
        """
        schema = {
            "_id": "ObjectId",  # MongoDB自动生成
            "original_url": "string",  # 原始链接
            "case_number": "string",   # 案号
            "case_name": "string",     # 案件名称
            "court": "string",         # 法院
            "region": "string",        # 所属地区 (可为null)
            "case_type": "string",     # 案件类型
            "case_type_code": "int",   # 案件类型编码
            "source": "string",        # 来源
            "trial_procedure": "string",  # 审理程序 (可为null)
            "judgment_date": "date",   # 裁判日期
            "publish_date": "date",    # 公开日期
            "parties": "string",       # 当事人 (可为null)
            "case_cause": "string",    # 案由 (可为null)
            "legal_basis": "string",   # 法律依据 (可为null)
            "full_text": "string",     # 全文 (可为null)
            
            # 元数据字段
            "import_info": {
                "file_source": "string",      # 来源文件名
                "import_time": "datetime",    # 导入时间
                "file_row_number": "int"      # 在原文件中的行号
            },
            
            # 索引优化字段
            "year": "int",                    # 年份 (从裁判日期提取)
            "month": "int",                   # 月份 (从裁判日期提取)
            "court_level": "string",          # 法院级别 (从法院名称推断)
            "region_province": "string",      # 省份 (从地区提取)
            
            # 全文搜索字段
            "search_text": "string"           # 组合搜索字段
        }
        return schema
    
    def create_indexes(self):
        """创建数据库索引以优化查询性能"""
        
        logger.info("开始创建数据库索引...")
        
        # 1. 基础查询索引
        indexes_to_create = [
            # 案号索引 (唯一性约束)
            ("case_number", ASCENDING),
            
            # 时间范围查询索引
            ("judgment_date", DESCENDING),
            ("publish_date", DESCENDING),
            ("year", DESCENDING),
            
            # 分类查询索引
            ("case_type", ASCENDING),
            ("case_type_code", ASCENDING),
            ("court", ASCENDING),
            ("region", ASCENDING),
            ("region_province", ASCENDING),
            ("court_level", ASCENDING),
            ("trial_procedure", ASCENDING),
            ("case_cause", ASCENDING),
            
            # 复合索引
            [("year", DESCENDING), ("case_type", ASCENDING)],
            [("region_province", ASCENDING), ("year", DESCENDING)],
            [("court", ASCENDING), ("judgment_date", DESCENDING)],
            [("case_type", ASCENDING), ("judgment_date", DESCENDING)],
            
            # 导入相关索引
            ("import_info.file_source", ASCENDING),
            ("import_info.import_time", DESCENDING),
        ]
        
        # 创建索引
        for index in indexes_to_create:
            try:
                if isinstance(index, tuple):
                    self.collection.create_index(index)
                    logger.info("创建索引: %s (%s)", index[0], index[1])
                else:
                    self.collection.create_index(index)
                    logger.info("创建复合索引: %s", index)
            except Exception as e:
                logger.error("创建索引失败 %s: %s", index, e)
        
        # 2. 全文搜索索引
        try:
            self.collection.create_index([
                ("case_name", TEXT),
                ("parties", TEXT),
                ("case_cause", TEXT),
                ("full_text", TEXT),
                ("search_text", TEXT)
            ], name="text_search_index")
            logger.info("创建全文搜索索引")
        except Exception as e:
            logger.error("创建全文搜索索引失败: %s", e)

        # 3. 案号唯一索引
        try:
            self.collection.create_index("case_number", unique=True, sparse=True)
            logger.info("创建案号唯一索引")
        except Exception as e:
            logger.error("创建案号唯一索引失败: %s", e)
        
        logger.info("索引创建完成")
    
    def setup_progress_tracking(self):
        """设置导入进度跟踪"""
        
        # 进度跟踪文档结构
        progress_schema = {
            "_id": "string",  # 文件名作为ID
            "file_path": "string",
            "total_rows": "int",
            "imported_rows": "int",
            "status": "string",  # pending, processing, completed, failed
            "start_time": "datetime",
            "end_time": "datetime",
            "error_message": "string",
            "last_row_processed": "int"
        }
        
        # 创建进度跟踪索引
        self.progress_collection.create_index("status")
        self.progress_collection.create_index("start_time")
        
        logger.info("进度跟踪设置完成")
    
    def setup_statistics(self):
        """设置统计信息收集"""
        
        # 统计信息文档结构
        stats_schema = {
            "_id": "string",  # 统计类型作为ID
            "type": "string",  # daily, monthly, yearly, total
            "date": "date",
            "counts": {
                "total_documents": "int",
                "by_case_type": "object",
                "by_court_level": "object",
                "by_region": "object",
                "by_year": "object"
            },
            "updated_time": "datetime"
        }
        
        # 创建统计索引
        self.stats_collection.create_index([("type", ASCENDING), ("date", DESCENDING)])
        
        logger.info("统计信息设置完成")
    
    def initialize_database(self):
        """初始化数据库结构"""
        
        logger.info("开始初始化数据库结构...")
        
        # 创建集合和索引
        self.create_indexes()
        
        # 设置进度跟踪
        self.setup_progress_tracking()
        
        # 设置统计信息
        self.setup_statistics()
        
        # 创建数据库配置文档
        config_doc = {
            "_id": "database_config",
            "version": "1.0",
            "created_time": datetime.now(),
            "schema_version": "1.0",
            "collections": {
                "documents": "主要文档集合",
                "import_progress": "导入进度跟踪",
                "statistics": "统计信息"
            }
        }
        
        try:
            self.db.config.insert_one(config_doc)
            logger.info("数据库配置文档创建完成")
        except Exception as e:
            logger.warning(f"配置文档可能已存在: {e}")
        
        logger.info("数据库初始化完成")
    
    def get_collection_info(self):
        """获取集合信息"""
        
        info = {
            "database": self.db.name,
            "collections": {
                "documents": {
                    "count": self.collection.count_documents({}),
                    "indexes": list(self.collection.list_indexes())
                },
                "import_progress": {
                    "count": self.progress_collection.count_documents({}),
                    "indexes": list(self.progress_collection.list_indexes())
                },
                "statistics": {
                    "count": self.stats_collection.count_documents({}),
                    "indexes": list(self.stats_collection.list_indexes())
                }
            }
        }
        
        return info
    
    def close_connection(self):
        """关闭数据库连接"""
        self.client.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数 - 初始化数据库"""
    
    # 创建数据库设计实例
    db_design = JudgmentDatabaseDesign()
    
    try:
        # 初始化数据库
        db_design.initialize_database()
        
        # 显示集合信息
        info = db_design.get_collection_info()
        print("\n数据库信息:")
        print(f"数据库名: {info['database']}")
        for coll_name, coll_info in info['collections'].items():
            print(f"集合 {coll_name}: {coll_info['count']} 个文档")
        
        print("\n数据库结构初始化完成!")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
    finally:
        db_design.close_connection()

if __name__ == "__main__":
    main()
