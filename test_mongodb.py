#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MongoDB连接
"""

from pymongo import MongoClient
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mongodb_connection():
    """测试MongoDB连接"""
    try:
        # 尝试连接MongoDB
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.server_info()
        logger.info("✓ MongoDB连接成功")
        
        # 测试数据库操作
        db = client.test_db
        collection = db.test_collection
        
        # 插入测试文档
        test_doc = {"test": "hello", "number": 123}
        result = collection.insert_one(test_doc)
        logger.info(f"✓ 插入文档成功: {result.inserted_id}")
        
        # 查询文档
        found_doc = collection.find_one({"test": "hello"})
        logger.info(f"✓ 查询文档成功: {found_doc}")
        
        # 创建索引
        collection.create_index("test")
        logger.info("✓ 创建索引成功")
        
        # 清理测试数据
        collection.delete_many({})
        client.drop_database("test_db")
        logger.info("✓ 清理测试数据成功")
        
        client.close()
        return True
        
    except Exception as e:
        logger.error(f"✗ MongoDB连接失败: {e}")
        return False

if __name__ == "__main__":
    print("测试MongoDB连接...")
    success = test_mongodb_connection()
    if success:
        print("MongoDB测试通过!")
    else:
        print("MongoDB测试失败!")
        print("请确保:")
        print("1. MongoDB服务正在运行")
        print("2. 连接字符串正确")
        print("3. 防火墙允许连接")
