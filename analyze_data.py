#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
裁判文书数据分析脚本
分析CSV文件结构和数据特征
"""

import pandas as pd
import os
from pathlib import Path

def analyze_judgment_data():
    """分析裁判文书数据结构"""
    
    # 收集所有CSV文件
    files = []
    base_path = Path("裁判文书")
    
    for year_dir in ["2021年", "2022年", "2023年", "2024年"]:
        year_path = base_path / year_dir
        if year_path.exists():
            for file in year_path.glob("*.csv"):
                files.append(str(file))
    
    print(f"总共找到 {len(files)} 个CSV文件")
    
    if not files:
        print("未找到CSV文件")
        return
    
    # 分析第一个文件作为样本
    sample_file = files[0]
    print(f"\n分析样本文件: {sample_file}")
    
    try:
        # 读取样本数据
        df = pd.read_csv(sample_file, encoding='utf-8')
        
        print(f"总行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n列名和数据类型:")
        for i, (col, dtype) in enumerate(df.dtypes.items()):
            print(f"{i+1:2d}. {col:15s} - {dtype}")
        
        print("\n各列缺失值统计:")
        null_counts = df.isnull().sum()
        for col, count in null_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{col:15s}: {count:6d} ({percentage:5.1f}%)")
        
        print("\n数据样本 (前3行):")
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 50)
        print(df.head(3).to_string())
        
        # 分析数据特征
        print("\n数据特征分析:")
        
        # 案件类型分布
        if '案件类型' in df.columns:
            print("\n案件类型分布:")
            case_types = df['案件类型'].value_counts()
            for case_type, count in case_types.head(10).items():
                print(f"  {case_type}: {count}")
        
        # 法院分布
        if '法院' in df.columns:
            print(f"\n法院数量: {df['法院'].nunique()}")
            print("主要法院:")
            courts = df['法院'].value_counts()
            for court, count in courts.head(5).items():
                print(f"  {court}: {count}")
        
        # 地区分布
        if '所属地区' in df.columns:
            print(f"\n地区数量: {df['所属地区'].nunique()}")
            print("主要地区:")
            regions = df['所属地区'].value_counts()
            for region, count in regions.head(5).items():
                print(f"  {region}: {count}")
        
        # 统计所有文件的总数据量
        print(f"\n统计所有文件数据量...")
        total_rows = 0
        file_stats = []
        
        for i, file_path in enumerate(files[:5]):  # 只检查前5个文件避免太慢
            try:
                temp_df = pd.read_csv(file_path, encoding='utf-8')
                rows = len(temp_df)
                total_rows += rows
                file_stats.append((file_path, rows))
                print(f"  {file_path}: {rows:,} 行")
            except Exception as e:
                print(f"  {file_path}: 读取失败 - {e}")
        
        print(f"\n前5个文件总计: {total_rows:,} 行")
        print(f"预估所有文件总数据量: {total_rows * len(files) // 5:,} 行")
        
    except Exception as e:
        print(f"分析文件时出错: {e}")

if __name__ == "__main__":
    analyze_judgment_data()
